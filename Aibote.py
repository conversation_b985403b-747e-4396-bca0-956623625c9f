# 1. 导入 AndroidBotMain 类
from PyAibote import AndroidBotMain
import time
def check_device_mode():
    #判断设备操作模式(无障碍/HID/异常)
    if CustomAndroidScript.click((0, 0)):  # 通过无障碍点击坐标判断Model模式 
        return "无障碍"
    else:
        # 初始化hid
        结果 = CustomAndroidScript.init_hid(obj.WinDriving)
        if 结果:
            return "HID"
        else:
            return False

# 2. 自定义一个脚本类，继承 AndroidBotMain
class CustomAndroidScript(AndroidBotMain):
    Log_Level = "DEBUG" # 设置是否终端打印输出 DEBUG：输出， INFO：不输出, 默认打印输出
    Log_Storage = True  # 终端打印信息是否存储LOG文件 True： 储存， False：不存储
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        connection_info = list()
        
    # 注意：script_main 此方法是脚本执行入口必须存在此方法,每个Android设备连接都会创建一个独立线程执行各种设备的脚本
    def script_main(self):  
        if check_device_mode():
            self.get_device_ip() #获取设备 IP
            self.get_android_id() #获取设备 ID
            self.get_group_id() #获取设备投屏 组号
            self.get_identifier() #获取设备投屏 编号
            self.get_title() #获取设备投屏 标题
            self.get_window_size() #获取设备 窗口大小
        e
        # 主函数死循环时手机app连接断开异常捕获跳出死循环demo示例代码
        while True:
            try:
                # 死循环中必须加入aibote函数代码
                self.get_installed_packages()
                print("我是个死循环")       
                time.sleep(2)
            # 服务端捕获客户端断开异常跳出线程循环结束连接
            except OSError as e:
                break
            # 捕获其他非连接断开异常
            except Exception as e:
                print(e)

if __name__ == '__main__':
    # 注意：此处监听的端口号，必须和手机端的脚本端口号一致
    # 监听 8888 号端口, 如果你想启动多个端口号给一个列表比如： [8888,7777,6666,5555]
    CustomAndroidScript.execute("0.0.0.0", 8888) #开启监听Android设备连接监听